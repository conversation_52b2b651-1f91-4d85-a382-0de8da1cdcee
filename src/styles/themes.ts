import {Dimensions, useColorScheme} from 'react-native';

export const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const [shortDimension, longDimension] =
  SCREEN_WIDTH < SCREEN_HEIGHT
    ? [SCREEN_WIDTH, SCREEN_HEIGHT]
    : [SCREEN_HEIGHT, SCREEN_WIDTH];

/* ============================================================================================== */
/*                                              HOOKS                                             */
/* ============================================================================================== */

export const useThemeColors = () => {
  const colorScheme = useColorScheme();
  return colorScheme === 'dark' ? colors.dark : colors.light;
};

/* ============================================================================================== */
/*                                              UTILS                                             */
/* ============================================================================================== */

const guidelineBaseWidth = 430;
const guidelineBaseHeight = 932;

export const scale = (size: number): number =>
  (shortDimension / guidelineBaseWidth) * size;
export const verticalScale = (size: number): number =>
  (longDimension / guidelineBaseHeight) * size;
export const moderateScale = (size: number, factor = 0.5): number =>
  size + (scale(size) - size) * factor;
export const moderateVerticalScale = (size: number, factor = 0.5): number =>
  size + (verticalScale(size) - size) * factor;

/* ============================================================================================== */
/*                                            CONSTANTS                                           */
/* ============================================================================================== */

const isSmallDevice = SCREEN_WIDTH < 375 || SCREEN_HEIGHT < 670;

import {baseColors} from './colors';

export const colors = {
  brand: {
    main: baseColors.primary[600],
    mainLight: baseColors.primary[500],
    coral: baseColors.secondary[600],
    coralLight: baseColors.secondary[500],
  },
  light: {
    // Light theme - Clean and minimal
    background: baseColors.base.white,
    backgroundSecondary: baseColors.neutral[50],
    backgroundTertiary: baseColors.neutral[100],
    text: baseColors.base.black,
    textSecondary: baseColors.neutral[700],
    textTertiary: baseColors.neutral[600],
    primary: baseColors.primary[600],
    primaryLight: baseColors.primary[500],
    primaryDark: baseColors.primary[700],
    secondary: baseColors.secondary[600],
    accent: baseColors.info[500],
    success: baseColors.success[600],
    warning: baseColors.warning[500],
    error: baseColors.error[600],
    border: baseColors.neutral[300],
    borderLight: baseColors.neutral[200],
    borderDark: baseColors.neutral[400],
    surface: baseColors.base.white,
    surfaceSecondary: baseColors.neutral[50],
    overlay: baseColors.special.overlayLight,
    shadow: baseColors.special.shadow,
  },
  dark: {
    // Dark theme - Kraken-inspired minimalist design
    background: '#0a0a0a',
    backgroundSecondary: '#151515',
    backgroundTertiary: '#1c1c1d',
    text: baseColors.base.white,
    textSecondary: '#e0e0e0',
    textTertiary: '#b0b0b0',
    primary: baseColors.primary[400],
    primaryLight: baseColors.primary[300],
    primaryDark: baseColors.primary[500],
    secondary: baseColors.secondary[500],
    accent: baseColors.info[400],
    success: baseColors.success[500],
    warning: baseColors.warning[400],
    error: baseColors.error[500],
    border: '#2a2a2a',
    borderLight: '#1f1f1f',
    borderDark: '#3a3a3a',
    surface: '#151515',
    surfaceSecondary: '#1c1c1d',
    overlay: baseColors.special.overlayDark,
    shadow: baseColors.special.shadowDark,
  },
  base: {
    white: baseColors.base.white,
    black: baseColors.base.black,
  },
  neutral: {
    50: baseColors.neutral[50],
    100: baseColors.neutral[100],
    200: baseColors.neutral[200],
    300: baseColors.neutral[300],
    400: baseColors.neutral[400],
    500: baseColors.neutral[500],
    600: baseColors.neutral[600],
    700: baseColors.neutral[700],
    800: baseColors.neutral[800],
    900: baseColors.neutral[900],
  },
};

export const fonts = {
  default: 'SF-Pro',
};

export const typography = {
  xs: moderateScale(12),
  sm: moderateScale(16),
  md: moderateScale(22),
  lg: moderateScale(26),
  xl: moderateScale(30),
  xxl: moderateScale(34),
};

export const spacing = {
  sm: moderateScale(8),
  md: moderateScale(12),
  lg: moderateScale(18),
  xl: moderateScale(24),
  xxl: moderateScale(34),
  xxxl: moderateScale(40),
} as const;

export const layout = {
  images: {
    xs: moderateScale(40),
    sm: moderateScale(80),
    md: moderateScale(150),
    lg: moderateScale(200),
  },
  ph: {
    sm: moderateScale(16),
    md: moderateScale(32),
    lg: moderateScale(48),
    screen: isSmallDevice ? 32 : 48,
    footer: isSmallDevice ? spacing.md * 2 : spacing.lg * 2,
  },
  pv: {
    sm: moderateScale(12),
    md: moderateScale(24),
    lg: moderateScale(34),
    screen: isSmallDevice ? spacing.sm : spacing.sm,
  },
  gap: {
    screen: spacing.lg,
  },
  borderRadius: {
    sm: moderateScale(8),
    md: moderateScale(14),
    lg: moderateScale(20),
  },
} as const;

/* ============================================================================================== */
/*                                            AS OF NOW                                           */
/* ============================================================================================== */

export interface IDefaultColors {
  accent: string;
  success: string;
  error: string;
  transparent: string;
}

export interface IThemeColors extends IDefaultColors {
  primary: string;
  secondary: string;
  background: string;
  onSurface: string;
  refreshControl: string;
}

interface IFont {
  fontFamily?: string;
}

interface IFonts {
  regular: IFont;
  medium: IFont;
  semiBold: IFont;
  bold: IFont;
  extraBold: IFont;
  black: IFont;
}

interface IDefaultThemeValues {
  colors: IDefaultColors;
  fonts: IFonts;
}

export interface ITheme extends IDefaultThemeValues {
  colors: IThemeColors;
}

const defaultThemeValues: IDefaultThemeValues = {
  colors: {
    accent: baseColors.primary[600],
    success: baseColors.success[600],
    error: baseColors.error[600],
    transparent: 'transparent',
  },
  fonts: {
    regular: {fontFamily: 'InterTight-Regular'},
    medium: {fontFamily: 'InterTight-Medium'},
    semiBold: {fontFamily: 'InterTight-SemiBold'},
    bold: {fontFamily: 'InterTight-Bold'},
    extraBold: {fontFamily: 'InterTight-ExtraBold'},
    black: {fontFamily: 'InterTight-Black'},
  },
};

const light: ITheme = {
  ...defaultThemeValues,
  colors: {
    ...defaultThemeValues.colors,
    primary: colors.light.text,
    secondary: colors.light.textSecondary,
    background: colors.light.background,
    onSurface: colors.light.borderLight,
    refreshControl: colors.light.text,
  },
};

const dark: ITheme = {
  ...defaultThemeValues,
  colors: {
    ...defaultThemeValues.colors,
    primary: colors.dark.text,
    secondary: colors.dark.textSecondary,
    background: colors.dark.background,
    onSurface: colors.dark.border,
    refreshControl: colors.dark.text,
  },
};

export const getTheme = (appTheme: string) => {
  return appTheme === 'dark' ? dark : light;
};

// Enhanced theme utilities
export const getThemedColors = (isDark: boolean) => {
  return isDark ? colors.dark : colors.light;
};

// Chart colors for risk-based visualization (as per user preference)
export const chartColors = {
  getRiskColor: (percentage: number): string => {
    if (percentage < 60) {
      return baseColors.success[500]; // Green for low risk
    } else if (percentage < 70) {
      return baseColors.warning[500]; // Yellow for medium risk
    } else if (percentage < 80) {
      return baseColors.error[500]; // Red for high risk
    } else {
      return baseColors.error[700]; // Darker red for very high risk
    }
  },
  success: baseColors.success[500],
  warning: baseColors.warning[500],
  error: baseColors.error[500],
  errorDark: baseColors.error[700],
};

// Semantic color mappings for common UI patterns
export const semanticColors = {
  light: {
    card: colors.light.surface,
    cardBorder: colors.light.borderLight,
    input: colors.light.surface,
    inputBorder: colors.light.border,
    inputFocus: colors.light.primary,
    button: colors.light.primary,
    buttonText: colors.light.background,
    link: colors.light.accent,
    divider: colors.light.borderLight,
    placeholder: colors.light.textTertiary,
  },
  dark: {
    card: colors.dark.surface,
    cardBorder: colors.dark.borderLight,
    input: colors.dark.surfaceSecondary,
    inputBorder: colors.dark.border,
    inputFocus: colors.dark.primary,
    button: colors.dark.primary,
    buttonText: colors.dark.background,
    link: colors.dark.accent,
    divider: colors.dark.borderLight,
    placeholder: colors.dark.textTertiary,
  },
};

export default {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  isSmallDevice,
  colors,
  fonts,
  typography,
  spacing,
  layout,
};
